'use client'

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { WidgetPreview } from '@/components/agents/widget-preview';
import { Clipboard } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { createClient } from '@/utils/supabase/client';
import { EmbedCodeModal } from '@/components/agents/embed-code-modal';

interface AgentWidgetPreviewProps {
  agentId: string;
  orgId: string;
}

export const AgentWidgetPreview = ({ agentId, orgId }: AgentWidgetPreviewProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [showEmbedModal, setShowEmbedModal] = useState(false);

  // Fetch widget settings just to know when they're loaded
  const { data: widgetConfig } = useQuery({
    queryKey: ['widget-config-page', agentId],
    queryFn: async () => {
      const supabase = createClient();
      const { data } = await supabase
        .from('agent_configs')
        .select('*')
        .eq('agent_id', agentId)
        .eq('config_type', 'internal')
        .single();
      return data;
    },
    enabled: !!agentId
  });

  // Set loading state when widget config is loaded
  useEffect(() => {
    if (widgetConfig) {
      setIsLoading(false);
    } else {
      // Set a timeout to stop showing loading after a reasonable time
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [widgetConfig]);

  if (isLoading) {
    return <div className="h-full flex items-center justify-center">Loading widget settings...</div>;
  }

  return (
    <div className="h-full flex flex-col">
      <div className='flex justify-between mb-4'>
        <div>
          <h1 className="text-2xl font-bold">Widget Preview</h1>
          <p className="text-muted-foreground">Test your agent as you make changes</p>
        </div>
        <div className='flex gap-2'>
          <Button
            variant="outline"
            onClick={() => setShowEmbedModal(true)}
          >
            <Clipboard className="h-4 w-4 mr-2" />
            Embed Code
          </Button>
        </div>
      </div>

      {/* Widget preview takes up the full height */}
      <div className="h-[calc(100vh-150px)] flex-1">
        <WidgetPreview agentId={agentId} orgId={orgId} />
      </div>

      {/* Embed code modal */}
      <EmbedCodeModal
        open={showEmbedModal}
        onOpenChange={setShowEmbedModal}
        agentId={agentId}
      />
    </div>
  );
};
