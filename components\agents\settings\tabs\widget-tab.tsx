'use client'

import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Card } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useWidgetContext } from '@/contexts/widget-context'
import { useQuery } from '@tanstack/react-query'
import { createClient } from '@/utils/supabase/client'
import { useWidgetStore } from '@/stores/widget-store'
import { SimpleCssEditor } from '../simple-css-editor'

interface WidgetTabProps {
  setIsDirty: (dirty: boolean) => void
  agentId?: string
}

export function AgentWidgetTab({ setIsDirty, agentId }: WidgetTabProps) {
  // Use the widget context for the form UI and Zustand store for global state
  const widgetContextValue = useWidgetContext();
  const widgetStore = useWidgetStore();

  // State for CSS editor mode (simple or advanced)
  const [cssMode, setCssMode] = useState<'simple' | 'advanced'>('simple');

  // Get the form data from the context
  const formData = widgetContextValue.widgetSettings;

  // Fetch widget settings from database
  const { data: widgetConfig } = useQuery({
    queryKey: ['widget-config-global', agentId],
    queryFn: async () => {
      if (!agentId) return null;
      const supabase = createClient();
      const { data } = await supabase
        .from('agent_configs')
        .select('*')
        .eq('agent_id', agentId)
        .eq('config_type', 'internal')
        .single();
      return data;
    },
    enabled: !!agentId
  });

  // Initialize global context with database settings
  useEffect(() => {
    if (agentId && widgetConfig?.custom_metadata) {
      try {
        const metadata = widgetConfig.custom_metadata as Record<string, any>;
        if (metadata.widget) {
          // Update both contexts with the same settings
          const settings = {
            position: metadata.widget.position || formData.position,
            customText: metadata.widget.customText || formData.customText,
            hideIcon: metadata.widget.hideIcon || formData.hideIcon,
            customIcon: metadata.widget.customIcon || formData.customIcon,
            customCSS: metadata.widget.customCSS || formData.customCSS,
            widgetType: metadata.widget.widgetType || formData.widgetType,
          };

          // Update the Zustand store
          widgetStore.updateWidgetSettings(agentId, settings);

          // Update the local context
          widgetContextValue.updateWidgetSettings(settings);
        }
      } catch (error) {
        console.error('Error parsing widget settings:', error);
      }
    }
  }, [widgetConfig, agentId]);

  // Update form data in both the context and Zustand store
  const updateForm = (updates: Partial<typeof formData>) => {
    // Update local context
    widgetContextValue.updateWidgetSettings(updates);

    // Update Zustand store if we have an agent ID
    if (agentId) {
      widgetStore.updateWidgetSettings(agentId, updates);
    }

    setIsDirty(true);

    // We don't need to dispatch an event here since the Zustand store already does it
    // This was causing an infinite loop
  }

  // Update custom text in both the context and Zustand store
  const updateCustomText = (key: keyof typeof formData.customText, value: string) => {
    // Update local context
    widgetContextValue.updateCustomText(key, value);

    // Update Zustand store if we have an agent ID
    if (agentId) {
      widgetStore.updateCustomText(agentId, key, value);
    }

    setIsDirty(true);

    // We don't need to dispatch an event here since the Zustand store already does it
    // This was causing an infinite loop
  }

  // Add hidden input fields to store widget settings in form submission
  const getWidgetSettingsInput = () => {
    return (
      <input
        type="hidden"
        name="widgetSettings"
        value={JSON.stringify({
          widget: {
            ...formData
          }
        })}
      />
    )
  }

  return (
    <div className="w-full space-y-8">
      {getWidgetSettingsInput()}

      <Card className="p-6">
          <div className="space-y-6">
            <div>
            <Label className="dark:text-zinc-100">Widget Type</Label>
            <Select
              value={formData.widgetType || 'standalone'}
              onValueChange={(value: 'standalone' | 'compact') => updateForm({ widgetType: value })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Select widget type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="standalone">Standalone</SelectItem>
                <SelectItem value="compact">Compact</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground mt-1">
              Standalone is the full widget, compact is a smaller version
            </p>
          </div>

          <div>
            <Label className="dark:text-zinc-100">Widget Position</Label>
            <Select
              value={formData.position}
              onValueChange={(value) => updateForm({ position: value })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bottom-right">Bottom Right</SelectItem>
                <SelectItem value="bottom-left">Bottom Left</SelectItem>
                <SelectItem value="top-right">Top Right</SelectItem>
                <SelectItem value="top-left">Top Left</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="dark:text-zinc-100">Hide Default Icon</Label>
              <p className="text-sm text-muted-foreground">Remove the default chat icon</p>
            </div>
            <Switch
              checked={formData.hideIcon}
              onCheckedChange={v => updateForm({ hideIcon: v })}
            />
          </div>

          <div>
            <Label className="dark:text-zinc-100">Start Call Button</Label>
            <Input
              value={formData.customText.startCall}
              onChange={e => updateCustomText('startCall', e.target.value)}
              className="mt-2"
            />
          </div>

          <div>
            <Label className="dark:text-zinc-100">End Call Button</Label>
            <Input
              value={formData.customText.endCall}
              onChange={e => updateCustomText('endCall', e.target.value)}
              className="mt-2"
            />
          </div>

          <div>
            <Label className="dark:text-zinc-100">Custom Icon (SVG)</Label>
            <textarea
              value={formData.customIcon}
              onChange={e => updateForm({ customIcon: e.target.value })}
              className="mt-2 w-full h-32 font-mono text-sm p-2 rounded-md border dark:border-zinc-700 dark:bg-zinc-900 dark:text-zinc-300"
              placeholder='<svg viewBox="0 0 24 24">...</svg>'
            />
            <p className="text-xs text-muted-foreground mt-1">
              Paste an SVG icon code to replace the default chat icon
            </p>
          </div>
        </div>

        <div className="mt-6">
          <Tabs
            value={cssMode}
            onValueChange={(value) => setCssMode(value as 'simple' | 'advanced')}
            className="w-full">
            <TabsList className="mb-4 w-full">
              <TabsTrigger value="simple" className="flex-1">Simple</TabsTrigger>
              <TabsTrigger value="advanced" className="flex-1">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="simple">
              <SimpleCssEditor
                value={formData.customCSS}
                onChange={(css: string) => updateForm({ customCSS: css })}
              />
            </TabsContent>

            <TabsContent value="advanced">
              <Label className="dark:text-zinc-100">Custom CSS</Label>
              <textarea
                value={formData.customCSS}
                onChange={e => updateForm({ customCSS: e.target.value })}
                className="mt-2 w-full h-64 font-mono text-sm p-2 rounded-md border dark:border-zinc-700 dark:bg-zinc-900 dark:text-zinc-300"
                placeholder={`/* Widget container */
::part(widget-container) {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Start button */
::part(start-button) {
  background-color: #2e62c9;
  font-family: "Inter",Sans-serif;
  color: #fff;
  border-radius: 8px;
}

/* End button */
::part(end-button) {
  background-color: #e12525;
  color: #fff;
  border-radius: 8px;
}`}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Use ::part() selectors to style widget elements
              </p>
            </TabsContent>
          </Tabs>
        </div>
      </Card>
    </div>
  )
}
