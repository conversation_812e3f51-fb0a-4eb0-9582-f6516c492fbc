'use client'

import { useState } from 'react'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card } from '@/components/ui/card'

interface AnalysisTabProps {
  setIsDirty: (dirty: boolean) => void
}

export function AgentAnalysisTab({ setIsDirty }: AnalysisTabProps) {
  const [formData, setFormData] = useState({
    sentiment: true,
    intentDetection: true,
    keywordTracking: true,
    analyticsLevel: 'basic',
    exportData: false,
  })

  const updateForm = (updates: Partial<typeof formData>) => {
    setFormData(prev => ({ ...prev, ...updates }))
    setIsDirty(true)
  }

  return (
    <div className="max-w-3xl space-y-8">
      <Card className="p-6 space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Sentiment Analysis</Label>
              <p className="text-sm text-muted-foreground">Track conversation sentiment</p>
            </div>
            <Switch
              checked={formData.sentiment}
              onCheckedChange={v => updateForm({ sentiment: v })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>Intent Detection</Label>
              <p className="text-sm text-muted-foreground">Identify user intents</p>
            </div>
            <Switch
              checked={formData.intentDetection}
              onCheckedChange={v => updateForm({ intentDetection: v })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>Keyword Tracking</Label>
              <p className="text-sm text-muted-foreground">Track important keywords</p>
            </div>
            <Switch
              checked={formData.keywordTracking}
              onCheckedChange={v => updateForm({ keywordTracking: v })}
            />
          </div>
        </div>

        <div>
          <Label>Analytics Level</Label>
          <Select
            value={formData.analyticsLevel}
            onValueChange={v => updateForm({ analyticsLevel: v })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="basic">Basic</SelectItem>
              <SelectItem value="advanced">Advanced</SelectItem>
              <SelectItem value="premium">Premium</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>
    </div>
  )
}
