import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import * as elevenlabsQueries from '@/utils/elevenlabs/queries';

/**
 * Get a signed URL for an ElevenLabs agent
 *
 * This endpoint:
 * 1. Takes an agent ID
 * 2. Retrieves the ElevenLabs agent ID from the database
 * 3. Checks if there's sufficient budget
 * 4. Returns a signed URL if budget is available, or an error message if not
 */
export async function GET(request: Request) {
  try {
    // Get the agent ID from the query parameters
    const url = new URL(request.url);
    const agentId = url.searchParams.get('agentId');

    if (!agentId) {
      return NextResponse.json(
        { error: 'Missing agentId parameter' },
        { status: 400 }
      );
    }

    // Default estimated cost (in cents)
    const estimatedCost = parseInt(url.searchParams.get('estimatedCost') || '10');

    // Create Supabase client
    const supabase = await createClient();

    // Get the agent to verify it exists
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('id', agentId)
      .single();

    if (agentError || !agent) {
      console.error('Error fetching agent:', agentError);
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      );
    }

    // Get the agent config to find the ElevenLabs agent ID
    // Look for a voice config with provider 'elevenlabs'
    const { data: agentConfig, error: configError } = await supabase
      .from('agent_configs')
      .select('*')
      .eq('agent_id', agentId)
      .eq('config_type', 'voice')
      .eq('provider', 'elevenlabs')
      .maybeSingle();

    if (configError) {
      console.error('Error fetching agent config:', configError);
      return NextResponse.json(
        { error: 'Error fetching agent configuration' },
        { status: 500 }
      );
    }

    // Extract the ElevenLabs agent ID from the external_provider_id field
    let elevenLabsAgentId: string | undefined;

    if (agentConfig && agentConfig.external_provider_id) {
      elevenLabsAgentId = agentConfig.external_provider_id;
    }

    if (!elevenLabsAgentId) {
      return NextResponse.json(
        { error: 'ElevenLabs agent ID not found for this agent' },
        { status: 404 }
      );
    }

    // Check if the agent has enough budget
    const { data: canStartCall, error: budgetError } = await supabase.rpc('can_start_call', {
      agent: agentId,
      estimated_cost: estimatedCost
    });

    if (budgetError) {
      console.error('Error checking budget:', budgetError);
      return NextResponse.json(
        {
          error: 'Error checking budget',
          details: budgetError.message
        },
        { status: 500 }
      );
    }

    if (!canStartCall) {
      // Determine the specific reason for insufficient budget
      const { data: orgBalance } = await supabase
        .from('credit_wallets')
        .select('balance_cents')
        .eq('organization_id', agent.organization_id as string)
        .single();

      if (orgBalance && orgBalance.balance_cents !== null && orgBalance.balance_cents < estimatedCost) {
        return NextResponse.json(
          {
            error: 'Insufficient organization credits',
            allow_call: false,
            details: 'The organization does not have enough credits for this call.'
          },
          { status: 403 }
        );
      }

      // Check agent daily limit
      const { data: agentLimit } = await supabase
        .from('agent_budget_limits')
        .select('daily_limit_cents')
        .eq('agent_id', agentId)
        .maybeSingle();

      if (agentLimit && agentLimit.daily_limit_cents) {
        const { data: agentUsage } = await supabase
          .from('credit_transactions')
          .select('amount_cents')
          .eq('agent_id', agentId)
          .gte('created_at', new Date(new Date().setHours(0,0,0,0)).toISOString());

        const dailyUsage = agentUsage ? agentUsage.reduce((sum, tx) => sum + Math.abs(tx.amount_cents || 0), 0) : 0;

        if (dailyUsage + estimatedCost > agentLimit.daily_limit_cents) {
          return NextResponse.json(
            {
              error: 'Agent daily budget limit reached',
              allow_call: false,
              details: 'This agent has reached its daily budget limit.'
            },
            { status: 403 }
          );
        }
      }

      // If we get here, it's likely a team budget limit
      if (agent.team_id) {
        return NextResponse.json(
          {
            error: 'Team budget limit reached',
            allow_call: false,
            details: 'The team this agent belongs to has reached its budget limit.'
          },
          { status: 403 }
        );
      }

      // Generic insufficient budget message as fallback
      return NextResponse.json(
        {
          error: 'Insufficient budget',
          allow_call: false,
          details: 'There are not enough credits available to start this call.'
        },
        { status: 403 }
      );
    }

    // Get the signed URL from ElevenLabs
    try {
      const signedUrl = await elevenlabsQueries.getSignedUrl(elevenLabsAgentId);

      return NextResponse.json({
        url: signedUrl,
        allow_call: true,
        agent_id: elevenLabsAgentId,
        success: true
      });
    } catch (error) {
      console.error('Error getting signed URL from ElevenLabs:', error);

      return NextResponse.json(
        {
          error: 'Failed to get signed URL from ElevenLabs',
          details: error instanceof Error ? error.message : String(error),
          success: false
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in signed URL endpoint:', error);

    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : String(error),
        success: false
      },
      { status: 500 }
    );
  }
}
