import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import { getOrganizationAgents } from '@/utils/supabase/queries'
import { createClient } from '@/utils/supabase/server'


import { ElevenLabsClient } from 'elevenlabs';
import { BodyCreateAgentV1ConvaiAgentsCreatePost } from 'elevenlabs/api';


export async function GET(
  request: Request,
  { params }: { params: Promise<{ orgId: string }> }
) {
  try {
    // Initialize Supabase client
    const supabase = await createClient()
    const {orgId} = await params;

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Verify organization access
    const { data: membership } = await supabase
      .from('organization_memberships')
      .select('organization_id')
      .eq('user_id', user.id)
      .eq('organization_id', orgId)
      .single()

    if (!membership) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '10')

    // Fetch agents using the existing query
    const { data, count, error, hasMore } = await getOrganizationAgents(
      supabase,
      orgId,
      search,
      page,
      pageSize
    )

    if (error) {
      return new NextResponse('Internal Server Error', { status: 500 })
    }

    // Return success response
    return NextResponse.json({
      data,
      count,
      hasMore,
      error: null
    })

  } catch (error) {
    console.error('Error in agents route:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}
export async function POST(
  request: Request,
  { params }: { params: Promise<{ orgId: string }> }
) {
  const { name, conversation_config, budget, team_id } = await request.json()
  const { orgId } = await params;

  try{
    const client = new ElevenLabsClient({
      apiKey: process.env.ELEVENLABS_API_KEY,
    });
    const formattedName = `${name} [SE-${orgId}]`;
    let conversationConfig:BodyCreateAgentV1ConvaiAgentsCreatePost = {
      name: formattedName,
      conversation_config
    };
    const elevenLabsAgent = await client.conversationalAi.createAgent(conversationConfig);
    console.log(elevenLabsAgent)
    // return NextResponse.json(agent)

  }catch(error){
    console.error('Error creating agent:', error)
    throw error
  }



  try {
    const supabase = await createClient()

    // Create the agent data object
    const agentData: any = {
      name,
      organization_id: orgId,
    };

    // Add team_id if provided and not 'none'
    if (team_id && team_id !== 'none') {
      agentData.team_id = team_id;
    }

    // Add budget if provided (convert to cents)
    if (budget !== undefined) {
      agentData.budget_cents = Math.round(budget * 100);
    }

    const { data, error } = await supabase
      .from('agents')
      .insert([agentData])
      .select()
      .single()

    if (error) {
      console.error('Error creating agent:', error)
      throw error
    }

    return NextResponse.json(data)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to create agent' },
      { status: 500 }
    )
  }
}
