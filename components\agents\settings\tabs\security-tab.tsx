'use client'

import { useState } from 'react'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface SecurityTabProps {
  setIsDirty: (dirty: boolean) => void
}

export function AgentSecurityTab({ setIsDirty }: SecurityTabProps) {
  const [formData, setFormData] = useState({
    ipWhitelist: '',
    requireAuth: true,
    sensitiveDataFiltering: true,
    accessKey: '',
    rateLimit: 100,
  })

  const updateForm = (updates: Partial<typeof formData>) => {
    setFormData(prev => ({ ...prev, ...updates }))
    setIsDirty(true)
  }

  return (
    <div className="max-w-3xl space-y-8">
      <Card className="p-6 space-y-6">
        <div>
          <Label>IP Whitelist</Label>
          <Input
            value={formData.ipWhitelist}
            onChange={e => updateForm({ ipWhitelist: e.target.value })}
            placeholder="Enter comma-separated IPs"
            className="mt-2"
          />
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Require Authentication</Label>
              <p className="text-sm text-muted-foreground">Require API key for access</p>
            </div>
            <Switch
              checked={formData.requireAuth}
              onCheckedChange={v => updateForm({ requireAuth: v })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>Sensitive Data Filtering</Label>
              <p className="text-sm text-muted-foreground">Filter PII and sensitive data</p>
            </div>
            <Switch
              checked={formData.sensitiveDataFiltering}
              onCheckedChange={v => updateForm({ sensitiveDataFiltering: v })}
            />
          </div>
        </div>

        <div>
          <Label>Rate Limit (requests/minute)</Label>
          <Input
            type="number"
            value={formData.rateLimit}
            onChange={e => updateForm({ rateLimit: parseInt(e.target.value) })}
            className="mt-2"
          />
        </div>

        <div>
          <Button variant="outline" onClick={() => updateForm({ accessKey: crypto.randomUUID() })}>
            Generate New Access Key
          </Button>
          {formData.accessKey && (
            <div className="mt-2 p-2 bg-muted rounded text-sm font-mono">
              {formData.accessKey}
            </div>
          )}
        </div>
      </Card>
    </div>
  )
}
