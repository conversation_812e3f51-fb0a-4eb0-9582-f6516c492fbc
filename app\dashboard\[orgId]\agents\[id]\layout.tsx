import { PropsWithChildren } from 'react'
import { cn } from "@/utils/cn"
import Link from 'next/link'
import { createClient } from '@/utils/supabase/server'
import { getOrganizationAgents, getAgentDetails } from '@/utils/supabase/queries'
import SearchList from '@/components/SearchList/search-list'
import { ListState, ListContext, BaseItem } from '@/types/list'
import { AgentItem } from '@/components/agent-list-components'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { AddAgentButton } from '@/components/agents/add-agent-button'
import { AgentSettingsSidebar } from '@/components/agents/settings/agent-settings-sidebar'

interface LayoutProps extends PropsWithChildren {
  params: Promise<{
    orgId: string,
    id: string
  }>,
  children: React.ReactNode
}

export default async function Layout({ children, params }: LayoutProps) {
  const {orgId, id} = await params;
  console.log('--',orgId, id);
  

  const supabase = await createClient();

  // Fetch agent details for the title
  const agent = await getAgentDetails(supabase, id);
  console.log(agent, 'agentDetaisls')

  return (
    <div className="@container/main grid grid-cols-12 h-full relative">
      {/* Left column: Agent settings sidebar */}
      <div className={cn(
        "border-r overflow-y-auto",
        "transition-[grid-column] duration-300 ease-in-out",
        "col-span-12 @xl/main:col-span-6 @2xl/main:col-span-5"
      )}>
        {/* Client-side settings form */}
        <AgentSettingsSidebar agentId={id} orgId={orgId} />
      </div>

      {/* Right column: Widget preview */}
      <div className={cn(
        "overflow-y-auto",
        "transition-[grid-column] duration-300 ease-in-out",
        "hidden @xl/main:block @xl/main:col-span-6 @2xl/main:col-span-7"
      )}>
        <div className="p-6 h-full">
          {children}
        </div>
      </div>

      {/* Mobile view - only show a back button */}
      <div className="@xl/main:hidden p-4">
        <Link
          href={`/dashboard/${orgId}/agents`}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          ← Back to Agents
        </Link>
      </div>
    </div>
  )
}