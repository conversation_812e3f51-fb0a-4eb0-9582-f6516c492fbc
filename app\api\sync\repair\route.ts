import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import * as elevenLabsSync from '@/utils/sync/elevenlabs-sync';

export async function POST(request: NextRequest) {
  try {
    const { agentId } = await request.json();

    if (!agentId) {
      return NextResponse.json(
        { error: 'Agent ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Verify if the agent exists
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('id', agentId)
      .single();

    if (agentError || !agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      );
    }

    // Get the agent config
    const { data: agentConfig, error: configError } = await supabase
      .from('agent_configs')
      .select('*')
      .eq('agent_id', agentId)
      .eq('config_type', 'internal')
      .maybeSingle();

    if (configError || !agentConfig) {
      return NextResponse.json(
        { error: 'Agent config not found' },
        { status: 404 }
      );
    }

    // Check if there's an ElevenLabs agent ID
    let elevenLabsAgentId: string | undefined;
    if (agentConfig.custom_metadata && typeof agentConfig.custom_metadata === 'object') {
      const metadata = agentConfig.custom_metadata as Record<string, any>;
      elevenLabsAgentId = metadata.externalAgentId;
    }

    // If there's an ElevenLabs agent ID, verify if it exists
    let needsRepair = true;
    if (elevenLabsAgentId) {
      const exists = await elevenLabsSync.verifyElevenLabsAgent(elevenLabsAgentId);
      needsRepair = !exists;
    }

    // If repair is needed, create a new ElevenLabs agent
    if (needsRepair) {
      const syncResult = await elevenLabsSync.repairElevenLabsSync(supabase, agentId);

      if (!syncResult.elevenLabsSuccess || !syncResult.supabaseSuccess) {
        return NextResponse.json(
          {
            error: 'Failed to synchronize agent',
            details: {
              message: 'An error occurred during synchronization'
            }
          },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Agent synchronized successfully'
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Agent is already synchronized'
    });
  } catch (error) {
    console.error('Error repairing synchronization:', error);
    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const agentId = request.nextUrl.searchParams.get('agentId');

  if (!agentId) {
    return NextResponse.json(
      { error: 'Agent ID is required' },
      { status: 400 }
    );
  }

  try {
    const supabase = await createClient();

    // Get the agent config
    const { data: agentConfig, error: configError } = await supabase
      .from('agent_configs')
      .select('*')
      .eq('agent_id', agentId)
      .eq('config_type', 'internal')
      .maybeSingle();

    if (configError || !agentConfig) {
      return NextResponse.json(
        { error: 'Agent config not found' },
        { status: 404 }
      );
    }

    // Check if there's an ElevenLabs agent ID
    let elevenLabsAgentId: string | undefined;
    if (agentConfig.custom_metadata && typeof agentConfig.custom_metadata === 'object') {
      const metadata = agentConfig.custom_metadata as Record<string, any>;
      elevenLabsAgentId = metadata.externalAgentId;
    }

    // Check if the agent needs synchronization
    let needsSync = !elevenLabsAgentId;

    if (elevenLabsAgentId) {
      const exists = await elevenLabsSync.verifyElevenLabsAgent(elevenLabsAgentId);
      needsSync = !exists;
    }

    // Return a simplified status that doesn't expose implementation details
    return NextResponse.json({
      success: true,
      syncStatus: {
        hasExternalId: !!elevenLabsAgentId,
        externalIdExists: !needsSync,
        needsSync: needsSync
      }
    });
  } catch (error) {
    console.error('Error checking synchronization:', error);
    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
