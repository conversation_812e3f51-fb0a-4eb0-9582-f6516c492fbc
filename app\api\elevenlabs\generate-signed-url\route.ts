import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import * as elevenlabsQueries from '@/utils/elevenlabs/queries';

// Define the type for the result from the can_start_call stored procedure
interface CallCheckResult {
  status: 'success' | 'error';
  message: string | null;
  elevenlabs_agent_id: string | null;
}

/**
 * Generate a signed URL for an ElevenLabs agent
 *
 * This endpoint:
 * 1. Takes an agent ID
 * 2. Retrieves the ElevenLabs agent ID from the database
 * 3. Checks if there's sufficient budget
 * 4. Returns a signed URL if budget is available, or an error message if not
 */

const ALLOWED_ORIGIN = '*';
  // process.env.NODE_ENV === 'production'
  //   ? 'https://app.example'
  //   : '*';

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': ALLOWED_ORIGIN,
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}

export async function GET() {
  return Response.json({ ok: true }, {
    headers: { 'Access-Control-Allow-Origin': ALLOWED_ORIGIN, 
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}


export async function POST(request: Request) {
  try {
    // Get the request body
    const body = await request.json();
    const { agentId } = body;

    if (!agentId) {
      return NextResponse.json(
        { error: 'Missing agentId parameter' },
        { status: 400 }
      );
    }

    // Default estimated cost (in cents)
    const estimatedCost = body.estimatedCost || 10;

    // Create Supabase client
    const supabase = await createClient();

    // Get the agent to verify it exists
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('id', agentId)
      .single();

      console.log('Agent data:', agent);
console.log('Agent error:', agentError);

    if (agentError || !agent) {
      console.error('Error fetching agent:', agentError);
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      );
    }

    // Use the improved stored procedure to check budget and get ElevenLabs agent ID
    
    const { data, error: budgetError } = await supabase.rpc('can_start_call', {
      agent: agentId,
      estimated_cost: estimatedCost
    });

    if (budgetError) {
      console.error('Error checking budget:', budgetError);
      return NextResponse.json(
        {
          error: 'Error checking budget',
          details: budgetError.message
        },
        { status: 500 }
      );
    }

    // Cast the result to our type - first to unknown to avoid TypeScript errors
    const result = data as unknown as CallCheckResult;
    console.log('Budget check result:', result);

    // Check if the result indicates an error
    if (!result || result.status === 'error') {
      // Determine the appropriate status code based on the error message
      let statusCode = 403; // Default to forbidden (budget issues)
      if (result?.message?.includes('not found')) {
        statusCode = 404; // Not found
      }

      return NextResponse.json(
        {
          error: result?.message || 'Unknown error',
          allow_call: false,
          details: result?.message || 'An error occurred while checking budget'
        },
        { status: statusCode }
      );
    }

    // If we get here, result contains the ElevenLabs agent ID
    const elevenLabsAgentId = result.elevenlabs_agent_id as string;

    // Get the signed URL from ElevenLabs
    try {
      const signedUrl = await elevenlabsQueries.getSignedUrl(elevenLabsAgentId);

      return NextResponse.json({
        url: signedUrl,
        allow_call: true,
        agent_id: elevenLabsAgentId,
        success: true
      });
    } catch (error) {
      console.error('Error getting signed URL from ElevenLabs:', error);

      return NextResponse.json(
        {
          error: 'Failed to get signed URL from ElevenLabs',
          details: error instanceof Error ? error.message : String(error),
          success: false
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in generate-signed-url endpoint:', error);

    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : String(error),
        success: false
      },
      { status: 500 }
    );
  }
}
