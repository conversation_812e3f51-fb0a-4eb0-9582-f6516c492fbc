'use client'

import { useState } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Card } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface AdvancedTabProps {
  setIsDirty: (dirty: boolean) => void
}

export function AgentAdvancedTab({ setIsDirty }: AdvancedTabProps) {
  const [formData, setFormData] = useState({
    timeoutSeconds: 30,
    retryAttempts: 3,
    cacheEnabled: true,
    debugMode: false,
    logLevel: 'info',
    maxConcurrentCalls: 10,
  })

  const updateForm = (updates: Partial<typeof formData>) => {
    setFormData(prev => ({ ...prev, ...updates }))
    setIsDirty(true)
  }

  return (
    <div className="max-w-3xl space-y-8">
      <Card className="p-6 space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>Timeout (seconds)</Label>
            <Input
              type="number"
              value={formData.timeoutSeconds}
              onChange={e => updateForm({ timeoutSeconds: parseInt(e.target.value) })}
              className="mt-2"
            />
          </div>

          <div>
            <Label>Retry Attempts</Label>
            <Input
              type="number"
              value={formData.retryAttempts}
              onChange={e => updateForm({ retryAttempts: parseInt(e.target.value) })}
              className="mt-2"
            />
          </div>
        </div>

        <div>
          <Label>Log Level</Label>
          <Select
            value={formData.logLevel}
            onValueChange={v => updateForm({ logLevel: v })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="debug">Debug</SelectItem>
              <SelectItem value="info">Info</SelectItem>
              <SelectItem value="warn">Warning</SelectItem>
              <SelectItem value="error">Error</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Response Caching</Label>
              <p className="text-sm text-muted-foreground">Cache frequent responses</p>
            </div>
            <Switch
              checked={formData.cacheEnabled}
              onCheckedChange={v => updateForm({ cacheEnabled: v })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>Debug Mode</Label>
              <p className="text-sm text-muted-foreground">Enable detailed logging</p>
            </div>
            <Switch
              checked={formData.debugMode}
              onCheckedChange={v => updateForm({ debugMode: v })}
            />
          </div>
        </div>

        <div>
          <Label>Max Concurrent Calls</Label>
          <Input
            type="number"
            value={formData.maxConcurrentCalls}
            onChange={e => updateForm({ maxConcurrentCalls: parseInt(e.target.value) })}
            className="mt-2"
          />
        </div>
      </Card>
    </div>
  )
}
